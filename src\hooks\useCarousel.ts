import { useState, useEffect, useCallback, useRef } from 'react';

export interface UseCarouselOptions {
  itemCount: number;
  autoPlayInterval?: number;
  pauseOnHover?: boolean;
}

export interface UseCarouselReturn {
  currentIndex: number;
  isPlaying: boolean;
  nextSlide: () => void;
  prevSlide: () => void;
  goToSlide: (index: number) => void;
  pause: () => void;
  resume: () => void;
  handleTouchStart: (e: React.TouchEvent) => void;
  handleTouchMove: (e: React.TouchEvent) => void;
  handleTouchEnd: () => void;
  handleMouseEnter: () => void;
  handleMouseLeave: () => void;
}

export const useCarousel = ({
  itemCount,
  autoPlayInterval = 3000,
  pauseOnHover = true,
}: UseCarouselOptions): UseCarouselReturn => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isPaused, setIsPaused] = useState(false);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const touchStartX = useRef<number>(0);
  const touchEndX = useRef<number>(0);
  const isTouching = useRef<boolean>(false);

  // 下一张幻灯片
  const nextSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % itemCount);
  }, [itemCount]);

  // 上一张幻灯片
  const prevSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + itemCount) % itemCount);
  }, [itemCount]);

  // 跳转到指定幻灯片
  const goToSlide = useCallback((index: number) => {
    setCurrentIndex(index);
  }, []);

  // 暂停轮播
  const pause = useCallback(() => {
    setIsPlaying(false);
    setIsPaused(true);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // 恢复轮播
  const resume = useCallback(() => {
    if (!isTouching.current) {
      setIsPlaying(true);
      setIsPaused(false);
    }
  }, []);

  // 触摸开始
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
    isTouching.current = true;
    pause();
  }, [pause]);

  // 触摸移动
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    touchEndX.current = e.touches[0].clientX;
  }, []);

  // 触摸结束
  const handleTouchEnd = useCallback(() => {
    if (!isTouching.current) return;
    
    const touchDiff = touchStartX.current - touchEndX.current;
    const minSwipeDistance = 50;

    if (Math.abs(touchDiff) > minSwipeDistance) {
      if (touchDiff > 0) {
        // 向左滑动，显示下一张
        nextSlide();
      } else {
        // 向右滑动，显示上一张
        prevSlide();
      }
    }

    isTouching.current = false;
    // 延迟恢复自动播放
    setTimeout(() => {
      if (!isTouching.current) {
        resume();
      }
    }, 2000);
  }, [nextSlide, prevSlide, resume]);

  // 鼠标进入
  const handleMouseEnter = useCallback(() => {
    if (pauseOnHover) {
      pause();
    }
  }, [pause, pauseOnHover]);

  // 鼠标离开
  const handleMouseLeave = useCallback(() => {
    if (pauseOnHover && !isTouching.current) {
      setTimeout(() => {
        if (!isTouching.current) {
          resume();
        }
      }, 1000);
    }
  }, [resume, pauseOnHover]);

  // 自动播放效果
  useEffect(() => {
    if (isPlaying && !isPaused && itemCount > 1) {
      intervalRef.current = setInterval(() => {
        nextSlide();
      }, autoPlayInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isPlaying, isPaused, nextSlide, autoPlayInterval, itemCount]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    currentIndex,
    isPlaying,
    nextSlide,
    prevSlide,
    goToSlide,
    pause,
    resume,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    handleMouseEnter,
    handleMouseLeave,
  };
};
