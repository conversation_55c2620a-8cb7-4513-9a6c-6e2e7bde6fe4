import { useMemo } from 'react'
import CardCarousel, { type CardData } from './components/CardCarousel'
import './App.css'

import trans1 from './assets/banner/trans1.jpg'
import trans2 from './assets/banner/trans2.jpg'
import trans3 from './assets/banner/trans3.jpg'
import trans4 from './assets/banner/trans4.jpg'
import trans5 from './assets/banner/trans5.jpg'

function App() {
  // 使用 useMemo 优化卡片数据
  const cardsData: CardData[] = useMemo(() => [
    {
      id: 1,
      title: '图1',
      content: '内容1',
      image: trans1,
      backgroundColor: '#1d262d',
    },
    {
      id: 2,
      title: '图2',
      content: '内容2',
      image: trans2,
      backgroundColor: '#1d262d',
    },
    {
      id: 3,
      title: '图3',
      content: '内容3',
      image: trans3,
      backgroundColor: '#1d262d',
    },
    {
      id: 4,
      title: '图4',
      content: '内容4',
      image: trans4,
      backgroundColor: '#1d262d',
    },
    {
      id: 5,
      title: '图5',
      content: '内容5',
      image: trans5,
      backgroundColor: '#1d262d',
    },
  ], []);

  return (
    <div className="app">
      <header className="app-header">
        <h1>卡片轮播组件</h1>
      </header>

      <main className="app-main">
        <CardCarousel
          cards={cardsData}
          autoPlayInterval={3000}
          pauseOnHover={true}
          className="main-carousel"
        />

      </main>

      <footer className="app-footer">
        <p>使用 React + TypeScript + Vite 构建</p>
      </footer>
    </div>
  )
}

export default App
