/* 应用全局样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  color: #333;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* 头部样式 */
.app-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem 1rem;
  border-radius: 1rem;
  backdrop-filter: blur(10px);
}

.app-header h1 {
  font-size: clamp(1.8rem, 5vw, 3rem);
  font-weight: 700;
  color: rgb(0, 0, 0);
}

.app-header p {
  font-size: clamp(1rem, 3vw, 1.2rem);
  font-weight: 300;
}

/* 主内容区域 */
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.main-carousel {
  margin: 0 auto;
}

/* 信息区域 */
.app-info {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.app-info h2 {
  font-size: clamp(1.4rem, 4vw, 2rem);
  margin-bottom: 1rem;
  text-align: center;
}

.app-info ul {
  list-style: none;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.app-info li {
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 0.5rem;
  font-size: clamp(0.9rem, 2.5vw, 1rem);
  color: #4a5568;
  border-left: 4px solid #667eea;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.app-info li:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 底部样式 */
.app-footer {
  text-align: center;
  padding: 2rem 1rem;
  margin-top: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.app-footer p {
  color: rgba(255, 255, 255, 0.8);
  font-size: clamp(0.9rem, 2.5vw, 1rem);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .app {
    padding: 0.5rem;
  }

  .app-header {
    margin-bottom: 1.5rem;
    padding: 1.5rem 1rem;
  }

  .app-main {
    gap: 1.5rem;
  }

  .app-info {
    padding: 1.5rem;
  }

  .app-info ul {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .app-info li {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .app {
    padding: 0.25rem;
  }

  .app-header {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .app-info {
    padding: 1rem;
  }

  .app-footer {
    padding: 1rem;
    margin-top: 1rem;
  }
}

/* 大屏幕优化 */
@media (min-width: 1024px) {
  .app {
    padding: 2rem;
  }

  .app-header {
    padding: 3rem 2rem;
  }

  .app-info ul {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .app-header,
  .app-info,
  .app-footer {
    border: 2px solid #333;
  }

  .app-info li {
    border: 1px solid #333;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .app-info li {
    transition: none;
  }

  .app-info li:hover {
    transform: none;
  }
}
