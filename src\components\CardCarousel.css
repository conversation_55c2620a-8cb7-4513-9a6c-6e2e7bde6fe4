/* 基础样式重置和根元素设置 */
:root {
  --carousel-height: 50vh;
  --card-padding: 1rem;
  --border-radius: 0.75rem;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --primary-color: #3b82f6;
  --text-color: #1f2937;
  --bg-color: #ffffff;
  --indicator-size: 0.75rem;
  --nav-size: 3rem;
}

/* 轮播容器 */
.carousel-container {
  position: relative;
  width: 100%;
  height: var(--carousel-height);
  max-width: 100vw;
  margin: 0 auto;
  overflow: hidden;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 6px -1px var(--shadow-color);
  background-color: var(--bg-color);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 空状态 */
.carousel-container.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9fafb;
}

.empty-state p {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

/* 轮播包装器 */
.carousel-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

/* 轮播轨道 */
.carousel-track {
  display: flex;
  height: 100%;
  transition: transform 0.3s ease-in-out;
  will-change: transform;
}

/* 卡片样式 */
.carousel-card {
  flex-shrink: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius);
}

/* 卡片图片 */
.card-image {
  width: 100%;
  height: 60%;
  overflow: hidden;
  position: relative;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.carousel-card:hover .card-image img {
  transform: scale(1.05);
}

/* 卡片内容 */
.card-content {
  flex: 1;
  padding: var(--card-padding);
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
}

.card-title {
  font-size: clamp(1.2rem, 4vw, 1.8rem);
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.card-text {
  font-size: clamp(0.9rem, 3vw, 1.1rem);
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  z-index: 10;
}

.indicator {
  width: var(--indicator-size);
  height: var(--indicator-size);
  border-radius: 50%;
  border: none;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.indicator:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.indicator.active {
  background-color: var(--primary-color);
  transform: scale(1.3);
}

/* 导航按钮 */
.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: var(--nav-size);
  height: var(--nav-size);
  border: none;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px var(--shadow-color);
}

.carousel-nav:hover {
  background-color: rgba(255, 255, 255, 1);
  transform: translateY(-50%) scale(1.1);
}

.carousel-nav.prev {
  left: 1rem;
}

.carousel-nav.next {
  right: 1rem;
}

/* 播放状态指示器 */
.play-status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.2rem;
  z-index: 10;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.carousel-container:hover .play-status {
  opacity: 1;
}

/* 移动端适配 */
@media (max-width: 768px) {
  :root {
    --carousel-height: 60vh;
    --card-padding: 0.75rem;
    --nav-size: 2.5rem;
    --indicator-size: 0.6rem;
  }

  .carousel-nav {
    width: var(--nav-size);
    height: var(--nav-size);
    font-size: 1.2rem;
  }

  .carousel-nav.prev {
    left: 0.5rem;
  }

  .carousel-nav.next {
    right: 0.5rem;
  }

  .carousel-indicators {
    bottom: 0.5rem;
    gap: 0.3rem;
  }

  .play-status {
    top: 0.5rem;
    right: 0.5rem;
    font-size: 1rem;
  }

  .card-content {
    padding: var(--card-padding);
  }
}

/* 小屏幕设备 */
@media (max-width: 480px) {
  :root {
    --carousel-height: 70vh;
    --card-padding: 0.5rem;
    --nav-size: 2rem;
    --indicator-size: 0.5rem;
  }

  .card-title {
    font-size: clamp(1rem, 5vw, 1.4rem);
  }

  .card-text {
    font-size: clamp(0.8rem, 4vw, 1rem);
    -webkit-line-clamp: 2;
  }
}

/* 大屏幕设备 */
@media (min-width: 1024px) {
  :root {
    --carousel-height: 45vh;
    --card-padding: 1.5rem;
  }

  .carousel-container {
    max-width: 90vw;
  }
}

/* 超大屏幕设备 */
@media (min-width: 1440px) {
  :root {
    --carousel-height: 40vh;
  }

  .carousel-container {
    max-width: 80vw;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .carousel-nav {
    opacity: 0.8;
  }

  .carousel-nav:hover {
    transform: translateY(-50%);
  }

  .indicator:hover {
    transform: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .carousel-container {
    border: 2px solid var(--text-color);
  }

  .indicator {
    border: 1px solid var(--text-color);
  }

  .carousel-nav {
    border: 1px solid var(--text-color);
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .carousel-track,
  .carousel-nav,
  .indicator,
  .card-image img {
    transition: none;
  }
}
