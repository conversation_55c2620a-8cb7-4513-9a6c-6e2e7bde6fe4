import React, { useMemo, useLayoutEffect, useRef } from 'react';
import { useCarousel } from '../hooks/useCarousel';
import './CardCarousel.css';

export interface CardData {
  id: string | number;
  title: string;
  content: string;
  image?: string;
  backgroundColor?: string;
}

export interface CardCarouselProps {
  cards: CardData[];
  autoPlayInterval?: number;
  pauseOnHover?: boolean;
  className?: string;
}

export default function CardCarousel(
  { cards,
    autoPlayInterval = 3000,
    pauseOnHover = true,
    className = '',
  }
    : CardCarouselProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const trackRef = useRef<HTMLDivElement>(null);

  const {
    currentIndex,
    isPlaying,
    nextSlide,
    prevSlide,
    goToSlide,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    handleMouseEnter,
    handleMouseLeave,
  } = useCarousel({
    itemCount: cards.length,
    autoPlayInterval,
    pauseOnHover,
  });

  // 为了实现无限轮播，我们需要在首尾各添加一张卡片
  const extendedCards = useMemo(() => {
    if (cards.length === 0) return [];
    if (cards.length === 1) return cards;
    return [cards[cards.length - 1], ...cards, cards[0]];
  }, [cards]);

  // 计算当前显示的索引（考虑扩展的卡片）
  const displayIndex = useMemo(() => currentIndex + 1, [currentIndex]);

  // 使用 useLayoutEffect 来处理无限轮播的位置重置 & 正确的 translateX 步长
  useLayoutEffect(() => {
    if (!trackRef.current || cards.length <= 1) return;
    const track = trackRef.current;

    // 单卡片所占的 translateX 百分比 = 100 / 总扩展卡片数
    const percentPerSlide = 100 / extendedCards.length;

    track.style.transition = 'transform 0.3s ease-in-out';
    track.style.transform = `translateX(-${displayIndex * percentPerSlide}%)`;

    const handleTransitionEnd = () => {
      // 边界复位（由于当前逻辑 currentIndex 使用 0..(cards.length-1)，不会真正落在克隆位，只保留逻辑以防后续拓展）
      if (currentIndex === 0) {
        track.style.transition = 'none';
        track.style.transform = `translateX(-${1 * percentPerSlide}%)`;
      } else if (currentIndex === cards.length - 1) {
        track.style.transition = 'none';
        track.style.transform = `translateX(-${cards.length * percentPerSlide}%)`;
      }
    };

    track.addEventListener('transitionend', handleTransitionEnd);
    return () => {
      track.removeEventListener('transitionend', handleTransitionEnd);
    };
  }, [displayIndex, currentIndex, cards.length, extendedCards.length]);

  if (cards.length === 0) {
    return (
      <div className={`carousel-container empty ${className}`}>
        <div className="empty-state">
          <p>暂无卡片数据</p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`carousel-container ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="carousel-wrapper">
        <div
          ref={trackRef}
          className="carousel-track"
          style={{ width: `${extendedCards.length * 100}%` }}
        >
          {extendedCards.map((card, index) => (
            <div
              key={`${card.id}-${index}`}
              className="carousel-card"
              style={{
                backgroundColor: card.backgroundColor || '#ffffff',
                width: `${100 / extendedCards.length}%`,
              }}
            >
              {card.image && (
                <div className="card-image">
                  <img src={card.image} alt={card.title} />
                </div>
              )}
              <div className="card-content">
                <h3 className="card-title">{card.title}</h3>
                <p className="card-text">{card.content}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="carousel-indicators">
        {cards.map((_, index) => (
          <button
            key={index}
            className={`indicator ${index === currentIndex ? 'active' : ''}`}
            onClick={() => goToSlide(index)}
          />
        ))}
      </div>

      <button className="carousel-nav prev" onClick={prevSlide} aria-label="上一张">‹</button>
      <button className="carousel-nav next" onClick={nextSlide} aria-label="下一张">›</button>

      <div className="play-status">{isPlaying ? '▶️' : '⏸️'}</div>
    </div>
  );
};

